{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Favicons -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="icon" type="image/svg+xml">
    <link href="{% static 'users/assets/img/apple-touch-icon.svg' %}" rel="apple-touch-icon">
    <!-- Fallback for older browsers -->
    <link href="{% static 'users/assets/img/favicon.svg' %}" rel="shortcut icon">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

    <title>Update Profile - QuickReceipt</title>
    <style>
      :root {
        --primary-color: #667eea;
        --primary-dark: #5a67d8;
        --secondary-color: #764ba2;
        --success-color: #48bb78;
        --danger-color: #f56565;
        --warning-color: #ed8936;
        --light-bg: #f7fafc;
        --card-bg: #ffffff;
        --text-primary: #2d3748;
        --text-secondary: #4a5568;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }

      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        padding: 2rem 1rem;
      }

      .profile-container {
        max-width: 800px;
        margin: 0 auto;
      }

      .profile-card {
        background: var(--card-bg);
        border-radius: 20px;
        box-shadow: var(--shadow-lg);
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .profile-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        position: relative;
      }

      .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.1;
      }

      .profile-title {
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 1;
      }

      .profile-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
        position: relative;
        z-index: 1;
      }

      .profile-body {
        padding: 2.5rem;
      }

      .form-section {
        margin-bottom: 2rem;
      }

      .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .section-icon {
        width: 20px;
        height: 20px;
        color: var(--primary-color);
      }

      /* Custom File Upload Styles */
      .file-upload-container {
        position: relative;
        margin-bottom: 1.5rem;
      }

      .file-upload-area {
        border: 2px dashed var(--border-color);
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        background: #fafafa;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }

      .file-upload-area:hover {
        border-color: var(--primary-color);
        background: #f0f4ff;
      }

      .file-upload-area.dragover {
        border-color: var(--primary-color);
        background: #e6f3ff;
        transform: scale(1.02);
      }

      .current-image {
        max-width: 120px;
        max-height: 120px;
        border-radius: 12px;
        box-shadow: var(--shadow-md);
        margin-bottom: 1rem;
        object-fit: cover;
      }

      .upload-icon {
        font-size: 2.5rem;
        color: var(--primary-color);
        margin-bottom: 1rem;
      }

      .upload-text {
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
      }

      .upload-hint {
        color: #a0aec0;
        font-size: 0.8rem;
      }

      .file-input {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
        z-index: 10;
      }

      /* Hide the default Django file input widget completely */
      .file-upload-area input[type="file"] {
        position: absolute !important;
        opacity: 0 !important;
        width: 100% !important;
        height: 100% !important;
        cursor: pointer !important;
        z-index: 10 !important;
      }

      /* Hide the ugly Django file widget elements */
      .file-upload-area .clearable-file-input {
        position: absolute !important;
        opacity: 0 !important;
        width: 100% !important;
        height: 100% !important;
        cursor: pointer !important;
        z-index: 10 !important;
      }

      .file-upload-area .clearable-file-input a,
      .file-upload-area .clearable-file-input br,
      .file-upload-area .clearable-file-input label {
        display: none !important;
      }

      /* Hide any text nodes and clear checkbox */
      .file-upload-area .clearable-file-input input[type="checkbox"] {
        display: none !important;
      }

      /* Additional hiding for Django file widget */
      .file-upload-area > p,
      .file-upload-area > a,
      .file-upload-area > br {
        display: none !important;
      }

      /* Hide any text content that might leak through */
      .file-upload-area .clearable-file-input::before,
      .file-upload-area .clearable-file-input::after {
        content: '' !important;
        display: none !important;
      }

      /* Ensure the upload area maintains its styling */
      .file-upload-area {
        position: relative;
        overflow: hidden;
      }

      /* Form Controls */
      .form-floating {
        margin-bottom: 1.5rem;
      }

      .form-floating > .form-control {
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 1rem 0.75rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #fafafa;
      }

      .form-floating > .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
      }

      .form-floating > label {
        color: var(--text-secondary);
        font-weight: 500;
      }

      .form-control:focus + label {
        color: var(--primary-color);
      }

      /* Submit Button */
      .submit-btn {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
        width: 100%;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .submit-btn:active {
        transform: translateY(0);
      }

      /* Error Styles */
      .error-message {
        color: var(--danger-color);
        font-size: 0.875rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        body {
          padding: 1rem 0.5rem;
        }

        .profile-body {
          padding: 1.5rem;
        }

        .profile-header {
          padding: 1.5rem;
        }

        .profile-title {
          font-size: 1.5rem;
        }
      }
    </style>
  </head>
  <body>
    {% include 'users/components/back_button.html' with fallback_url='welcome' button_text='Back' %}

    <div class="profile-container">
      <div class="profile-card">
        <!-- Header Section -->
        <div class="profile-header">
          <h1 class="profile-title">Update Your Profile</h1>
          <p class="profile-subtitle">Customize your business information and branding</p>
        </div>

        <!-- Form Body -->
        <div class="profile-body">
          <form method="POST" enctype="multipart/form-data" id="profileForm">
            {% csrf_token %}

            <!-- Company Logo Section -->
            <div class="form-section">
              <div class="section-title">
                <i class="bi bi-image section-icon"></i>
                Company Logo
              </div>

              <div class="file-upload-container">
                <div class="file-upload-area" id="fileUploadArea">
                  {% if form.company_logo.value %}
                    <img src="{{ form.company_logo.value.url }}" alt="Current Logo" class="current-image" id="currentImage">
                  {% endif %}

                  <div class="upload-content" id="uploadContent">
                    <i class="bi bi-cloud-upload upload-icon"></i>
                    <div class="upload-text">
                      <strong>Click to upload</strong> or drag and drop
                    </div>
                    <div class="upload-hint">
                      PNG, JPG, GIF up to 10MB
                    </div>
                  </div>

                  {{ form.company_logo }}
                </div>

                {% if form.company_logo.errors %}
                  <div class="error-message">
                    <i class="bi bi-exclamation-circle"></i>
                    {{ form.company_logo.errors.0 }}
                  </div>
                {% endif %}
              </div>
            </div>

            <!-- Company Information Section -->
            <div class="form-section">
              <div class="section-title">
                <i class="bi bi-building section-icon"></i>
                Company Information
              </div>

              <div class="row">
                <div class="col-md-6">
                  <div class="form-floating">
                    {{ form.company_name }}
                    <label for="{{ form.company_name.id_for_label }}">Company Name</label>
                  </div>
                  {% if form.company_name.errors %}
                    <div class="error-message">
                      <i class="bi bi-exclamation-circle"></i>
                      {{ form.company_name.errors.0 }}
                    </div>
                  {% endif %}
                </div>

                <div class="col-md-6">
                  <div class="form-floating">
                    {{ form.whatsapp_contact }}
                    <label for="{{ form.whatsapp_contact.id_for_label }}">WhatsApp Contact</label>
                  </div>
                  {% if form.whatsapp_contact.errors %}
                    <div class="error-message">
                      <i class="bi bi-exclamation-circle"></i>
                      {{ form.whatsapp_contact.errors.0 }}
                    </div>
                  {% endif %}
                </div>
              </div>

              <div class="form-floating">
                {{ form.company_details }}
                <label for="{{ form.company_details.id_for_label }}">Company Details</label>
              </div>
              {% if form.company_details.errors %}
                <div class="error-message">
                  <i class="bi bi-exclamation-circle"></i>
                  {{ form.company_details.errors.0 }}
                </div>
              {% endif %}

              <div class="form-floating">
                {{ form.address }}
                <label for="{{ form.address.id_for_label }}">Business Address</label>
              </div>
              {% if form.address.errors %}
                <div class="error-message">
                  <i class="bi bi-exclamation-circle"></i>
                  {{ form.address.errors.0 }}
                </div>
              {% endif %}
            </div>

            <!-- Submit Button -->
            <button type="submit" class="submit-btn">
              <i class="bi bi-check-circle me-2"></i>
              Update Profile
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.querySelector('input[type="file"]');
        const fileUploadArea = document.getElementById('fileUploadArea');
        const currentImage = document.getElementById('currentImage');
        const uploadContent = document.getElementById('uploadContent');

        // Hide any Django file widget elements that might be showing
        function hideFileWidgetElements() {
          // Hide all unwanted elements in the upload area
          const unwantedElements = fileUploadArea.querySelectorAll('a, br, p, label:not(.upload-text):not(.upload-hint)');
          unwantedElements.forEach(element => {
            element.style.display = 'none';
          });

          // Hide checkboxes
          const checkboxes = fileUploadArea.querySelectorAll('input[type="checkbox"]');
          checkboxes.forEach(checkbox => {
            checkbox.style.display = 'none';
          });

          // Remove any text nodes that contain file paths or "Clear" text
          const walker = document.createTreeWalker(
            fileUploadArea,
            NodeFilter.SHOW_TEXT,
            {
              acceptNode: function(node) {
                // Only process text nodes that contain unwanted content
                if (node.textContent.includes('Currently:') ||
                    node.textContent.includes('Clear') ||
                    node.textContent.includes('.jpg') ||
                    node.textContent.includes('.png') ||
                    node.textContent.includes('.gif') ||
                    node.textContent.includes('company_logos/')) {
                  return NodeFilter.FILTER_ACCEPT;
                }
                return NodeFilter.FILTER_REJECT;
              }
            },
            false
          );

          let textNode;
          while (textNode = walker.nextNode()) {
            textNode.textContent = '';
          }
        }

        // Call the function to hide elements
        hideFileWidgetElements();

        // Re-run the hiding function after a short delay to catch any dynamically added elements
        setTimeout(hideFileWidgetElements, 100);

        // Make the entire upload area clickable
        if (fileUploadArea) {
          fileUploadArea.addEventListener('click', function(e) {
            // Find the actual file input (it might be nested)
            const actualFileInput = fileUploadArea.querySelector('input[type="file"]') || fileInput;
            if (actualFileInput && e.target !== actualFileInput) {
              e.preventDefault();
              e.stopPropagation();
              actualFileInput.click();
            }
          });

          // Handle file selection - attach to all file inputs in the area
          const allFileInputs = fileUploadArea.querySelectorAll('input[type="file"]');
          allFileInputs.forEach(input => {
            input.addEventListener('change', function(e) {
              const file = e.target.files[0];
              if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                  if (currentImage) {
                    currentImage.src = e.target.result;
                    currentImage.style.display = 'block';
                  } else {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'current-image';
                    img.id = 'currentImage';
                    fileUploadArea.insertBefore(img, uploadContent);
                  }
                  uploadContent.style.display = 'none';
                };
                reader.readAsDataURL(file);
              }
            });
          });
        }

        // Add Bootstrap classes to form controls
        const textInputs = document.querySelectorAll('input[type="text"], textarea');
        textInputs.forEach(input => {
          input.classList.add('form-control');
        });

        // Drag and drop functionality
        if (fileUploadArea) {
          ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            fileUploadArea.addEventListener(eventName, preventDefaults, false);
          });

          function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
          }

          ['dragenter', 'dragover'].forEach(eventName => {
            fileUploadArea.addEventListener(eventName, highlight, false);
          });

          ['dragleave', 'drop'].forEach(eventName => {
            fileUploadArea.addEventListener(eventName, unhighlight, false);
          });

          function highlight(e) {
            fileUploadArea.classList.add('dragover');
          }

          function unhighlight(e) {
            fileUploadArea.classList.remove('dragover');
          }

          fileUploadArea.addEventListener('drop', handleDrop, false);

          function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
              fileInput.files = files;
              const event = new Event('change', { bubbles: true });
              fileInput.dispatchEvent(event);
            }
          }
        }

        // Form submission loading state
        const form = document.getElementById('profileForm');
        const submitBtn = document.querySelector('.submit-btn');

        if (form && submitBtn) {
          form.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Updating...';
            submitBtn.disabled = true;
          });
        }
      });
    </script>
  </body>
</html>
